/**
 * Express server for Guidant Mastra AI Research Engine
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { <PERSON><PERSON> } from '@mastra/core';
import { createLogger } from './utils/logger';
import { Config } from './utils/config';
import { z } from 'zod';
import { createGuidantIntegration, GuidantMastraIntegration } from './integration';

const logger = createLogger('server');

// Request validation schemas
const ResearchRequestSchema = z.object({
  query: z.string().min(1),
  context: z.object({
    sessionId: z.string().optional(),
    queryType: z.enum(['technology', 'market', 'ux', 'general']).optional(),
    technologies: z.array(z.string()).optional(),
    targetMarket: z.string().optional(),
    competitors: z.array(z.string()).optional()
  }).optional()
});

const WorkflowExecutionSchema = z.object({
  workflowName: z.string(),
  inputData: z.record(z.any())
});

export function createServer(mastra: Mastra, config: Config) {
  const app = express();

  // Initialize Guidant integration
  let guidantIntegration: GuidantMastraIntegration | null = null;

  // Initialize integration asynchronously
  createGuidantIntegration(mastra).then(integration => {
    guidantIntegration = integration;
    logger.info('Guidant integration initialized successfully');
  }).catch(error => {
    logger.error('Failed to initialize Guidant integration:', error);
  });

  // Middleware
  app.use(helmet());
  app.use(compression());
  app.use(cors(config.server.cors));
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true }));

  // Request logging middleware
  app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
      const duration = Date.now() - start;
      logger.info('Request completed', {
        method: req.method,
        url: req.url,
        status: res.statusCode,
        duration: `${duration}ms`,
        userAgent: req.get('User-Agent')
      });
    });
    next();
  });

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      workflows: Object.keys(mastra.workflows),
      mcpServers: Object.keys(mastra.mcpServers)
    });
  });

  // Research endpoint - main entry point for research requests
  app.post('/research', async (req, res) => {
    try {
      const { query, context = {} } = ResearchRequestSchema.parse(req.body);
      
      logger.info('Research request received', { query, context });

      // Execute the research orchestrator workflow
      const workflow = mastra.workflows.researchOrchestrator;
      if (!workflow) {
        return res.status(500).json({
          error: 'Research orchestrator workflow not found'
        });
      }

      const run = workflow.createRun();
      const result = await run.start({
        inputData: {
          query,
          context: {
            sessionId: context.sessionId || generateSessionId(),
            queryType: context.queryType || classifyQuery(query),
            technologies: context.technologies || [],
            targetMarket: context.targetMarket,
            competitors: context.competitors || []
          }
        }
      });

      logger.info('Research request completed', { 
        sessionId: context.sessionId,
        resultType: typeof result 
      });

      res.json({
        success: true,
        data: result,
        metadata: {
          sessionId: context.sessionId,
          timestamp: new Date().toISOString(),
          workflowId: run.id
        }
      });

    } catch (error) {
      logger.error('Research request failed:', error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Invalid request format',
          details: error.errors
        });
      }

      res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Workflow execution endpoint - for direct workflow execution
  app.post('/workflows/:workflowName/execute', async (req, res) => {
    try {
      const { workflowName } = req.params;
      const { inputData } = WorkflowExecutionSchema.parse(req.body);

      logger.info('Workflow execution request', { workflowName, inputData });

      const workflow = mastra.workflows[workflowName];
      if (!workflow) {
        return res.status(404).json({
          error: `Workflow '${workflowName}' not found`
        });
      }

      const run = workflow.createRun();
      const result = await run.start({ inputData });

      logger.info('Workflow execution completed', { workflowName, runId: run.id });

      res.json({
        success: true,
        data: result,
        metadata: {
          workflowName,
          runId: run.id,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Workflow execution failed:', error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Invalid request format',
          details: error.errors
        });
      }

      res.status(500).json({
        error: 'Workflow execution failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // List available workflows
  app.get('/workflows', (req, res) => {
    const workflows = Object.keys(mastra.workflows).map(name => ({
      name,
      description: `${name} workflow for autonomous research`
    }));

    res.json({
      workflows,
      count: workflows.length
    });
  });

  // Guidant Integration Endpoints

  // Enhanced research endpoint using Guidant integration
  app.post('/guidant/research', async (req, res) => {
    try {
      if (!guidantIntegration) {
        return res.status(503).json({
          error: 'Guidant integration not available'
        });
      }

      const { query, context = {} } = ResearchRequestSchema.parse(req.body);

      logger.info('Guidant research request received', { query, context });

      const result = await guidantIntegration.executeResearch(query, context);

      res.json({
        success: true,
        data: result,
        metadata: {
          provider: 'guidant-mastra-integration',
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Guidant research request failed:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Invalid request format',
          details: error.errors
        });
      }

      res.status(500).json({
        error: 'Research request failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Market research endpoint
  app.post('/guidant/market-research', async (req, res) => {
    try {
      if (!guidantIntegration) {
        return res.status(503).json({
          error: 'Guidant integration not available'
        });
      }

      const { sessionId, researchQueries, targetMarket, competitors, focusAreas } = req.body;

      if (!sessionId || !researchQueries || !targetMarket) {
        return res.status(400).json({
          error: 'Missing required fields: sessionId, researchQueries, targetMarket'
        });
      }

      logger.info('Market research request', { sessionId, targetMarket });

      const result = await guidantIntegration.conductMarketResearch(
        sessionId,
        researchQueries,
        targetMarket,
        { competitors, focusAreas }
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('Market research failed:', error);
      res.status(500).json({
        error: 'Market research failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Technical validation endpoint
  app.post('/guidant/technical-validation', async (req, res) => {
    try {
      if (!guidantIntegration) {
        return res.status(503).json({
          error: 'Guidant integration not available'
        });
      }

      const { sessionId, technologies, projectType, constraints, features } = req.body;

      if (!sessionId || !technologies || !projectType) {
        return res.status(400).json({
          error: 'Missing required fields: sessionId, technologies, projectType'
        });
      }

      logger.info('Technical validation request', { sessionId, technologies, projectType });

      const result = await guidantIntegration.validateTechnicalFeasibility(
        sessionId,
        technologies,
        projectType,
        { constraints, features }
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('Technical validation failed:', error);
      res.status(500).json({
        error: 'Technical validation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // UX research endpoint
  app.post('/guidant/ux-research', async (req, res) => {
    try {
      if (!guidantIntegration) {
        return res.status(503).json({
          error: 'Guidant integration not available'
        });
      }

      const { sessionId, targetUrls, analysisType = 'comprehensive' } = req.body;

      if (!sessionId || !targetUrls) {
        return res.status(400).json({
          error: 'Missing required fields: sessionId, targetUrls'
        });
      }

      logger.info('UX research request', { sessionId, targetUrls, analysisType });

      const result = await guidantIntegration.conductUXResearch(
        sessionId,
        targetUrls,
        analysisType
      );

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('UX research failed:', error);
      res.status(500).json({
        error: 'UX research failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Integration health check
  app.get('/guidant/health', async (req, res) => {
    try {
      if (!guidantIntegration) {
        return res.status(503).json({
          status: 'unavailable',
          message: 'Guidant integration not initialized'
        });
      }

      const health = await guidantIntegration.healthCheck();

      const statusCode = health.status === 'healthy' ? 200 :
                        health.status === 'degraded' ? 206 : 503;

      res.status(statusCode).json(health);

    } catch (error) {
      logger.error('Health check failed:', error);
      res.status(500).json({
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Error handling middleware
  app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
    logger.error('Unhandled error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
  });

  // 404 handler
  app.use((req, res) => {
    res.status(404).json({
      error: 'Not found',
      message: `Route ${req.method} ${req.path} not found`
    });
  });

  return app;
}

/**
 * Generate a unique session ID
 */
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Classify query type based on content
 */
function classifyQuery(query: string): 'technology' | 'market' | 'ux' | 'general' {
  const lowerQuery = query.toLowerCase();
  
  if (lowerQuery.includes('technology') || lowerQuery.includes('framework') || 
      lowerQuery.includes('library') || lowerQuery.includes('api')) {
    return 'technology';
  }
  
  if (lowerQuery.includes('market') || lowerQuery.includes('competitor') || 
      lowerQuery.includes('business') || lowerQuery.includes('industry')) {
    return 'market';
  }
  
  if (lowerQuery.includes('ui') || lowerQuery.includes('ux') || 
      lowerQuery.includes('design') || lowerQuery.includes('interface')) {
    return 'ux';
  }
  
  return 'general';
}

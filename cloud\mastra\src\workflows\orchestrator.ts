/**
 * Research Orchestrator Workflow
 *
 * Main workflow that classifies research queries and routes them to
 * appropriate specialized workflows (Technology, Market, or UX research).
 */

import { createWorkflow, createStep } from '@mastra/core/workflows';
import { z } from 'zod';
import { technicalDocumentationWorkflow } from './technology.js';
import { marketResearchWorkflow } from './market.js';
import { uxResearchWorkflow } from './ux.js';

// Ensure workflows are committed
technicalDocumentationWorkflow.commit();
marketResearchWorkflow.commit();
uxResearchWorkflow.commit();

// Input/Output schemas
const OrchestratorInputSchema = z.object({
  query: z.string(),
  context: z.object({
    sessionId: z.string(),
    userId: z.string().optional(),
    priority: z.enum(['low', 'medium', 'high']).default('medium')
  })
});

const OrchestratorOutputSchema = z.object({
  queryType: z.enum(['technical', 'market', 'ux']),
  routedTo: z.string(),
  results: z.any(),
  metadata: z.object({
    processingTime: z.number(),
    confidence: z.number()
  })
});

// Query Classification Step
const queryClassificationStep = createStep({
  id: 'query-classification',
  inputSchema: OrchestratorInputSchema,
  outputSchema: z.object({
    queryType: z.enum(['technical', 'market', 'ux']),
    confidence: z.number(),
    originalQuery: z.string()
  }),
  execute: async ({ inputData }) => {
    const { query } = inputData;

    console.log('Classifying research query:', query);

    // Simple classification logic - in production, use ML model
    const queryLower = query.toLowerCase();

    let queryType: 'technical' | 'market' | 'ux';
    let confidence = 0.8;

    if (queryLower.includes('tech') || queryLower.includes('api') ||
        queryLower.includes('framework') || queryLower.includes('library') ||
        queryLower.includes('architecture') || queryLower.includes('implementation')) {
      queryType = 'technical';
    } else if (queryLower.includes('market') || queryLower.includes('competitor') ||
               queryLower.includes('pricing') || queryLower.includes('industry') ||
               queryLower.includes('business') || queryLower.includes('opportunity')) {
      queryType = 'market';
    } else if (queryLower.includes('ux') || queryLower.includes('ui') ||
               queryLower.includes('user') || queryLower.includes('design') ||
               queryLower.includes('interface') || queryLower.includes('experience')) {
      queryType = 'ux';
    } else {
      // Default to technical research
      queryType = 'technical';
      confidence = 0.6;
    }

    console.log('Query classified:', { queryType, confidence });

    return {
      queryType,
      confidence,
      originalQuery: query
    };
  }
});

// Result Synthesis Step
const resultSynthesisStep = createStep({
  id: 'result-synthesis',
  description: 'Synthesize research results from different workflow types',
  inputSchema: z.object({
    queryType: z.enum(['technical', 'market', 'ux']),
    results: z.any(),
    confidence: z.number(),
    originalQuery: z.string(),
    context: z.object({
      sessionId: z.string(),
      userId: z.string().optional(),
      priority: z.enum(['low', 'medium', 'high']).default('medium')
    })
  }),
  outputSchema: OrchestratorOutputSchema,
  execute: async ({ inputData }) => {
    console.log('Synthesizing research results');

    return {
      queryType: inputData.queryType,
      routedTo: `${inputData.queryType}-research-workflow`,
      results: inputData.results,
      metadata: {
        processingTime: Date.now(),
        confidence: inputData.confidence
      }
    };
  }
});

// Create the orchestrator workflow following architecture document pattern
export const researchOrchestrator = createWorkflow({
  id: "research-orchestrator",
  inputSchema: OrchestratorInputSchema,
  outputSchema: OrchestratorOutputSchema
})
.then(queryClassificationStep)
.branch([
  [
    async ({ inputData }) => inputData.queryType === 'technical',
    technicalDocumentationWorkflow as any
  ],
  [
    async ({ inputData }) => inputData.queryType === 'market',
    marketResearchWorkflow as any
  ],
  [
    async ({ inputData }) => inputData.queryType === 'ux',
    uxResearchWorkflow as any
  ]
])
.then(resultSynthesisStep);

researchOrchestrator.commit();
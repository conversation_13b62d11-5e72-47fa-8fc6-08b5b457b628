/**
 * Discovery Session Manager Adapter for Mastra AI Integration
 * 
 * Integrates Mastra AI workflows with Guidant's existing DiscoverySessionManager
 * to provide autonomous research capabilities during discovery sessions.
 */

import { Mastra } from '@mastra/core';
import { MastraResearchRouterAdapter } from './research-router-adapter';

interface DiscoverySession {
  sessionId: string;
  projectName: string;
  stage: string;
  data: any;
  timestamp: string;
}

interface MarketResearchRequest {
  sessionId: string;
  researchQueries: string[];
  targetMarket: string;
  competitors?: string[];
  focusAreas?: string[];
}

interface TechnicalValidationRequest {
  sessionId: string;
  technologies: string[];
  projectType: string;
  constraints?: string[];
  features?: string[];
}

interface RequirementsSynthesis {
  sessionId: string;
  problemStatement: string;
  targetUsers: string[];
  functionalRequirements: any[];
  nonFunctionalRequirements?: any[];
}

export class MastraDiscoverySessionAdapter {
  private mastra: Mastra;
  private researchRouter: MastraResearchRouterAdapter;
  private fallbackSessionManager?: any; // Original DiscoverySessionManager

  constructor(mastra: Mastra, fallbackSessionManager?: any) {
    this.mastra = mastra;
    this.researchRouter = new MastraResearchRouterAdapter(mastra);
    this.fallbackSessionManager = fallbackSessionManager;
  }

  /**
   * Conduct autonomous market research using Mastra workflows
   */
  async conductMarketResearch(request: MarketResearchRequest): Promise<any> {
    try {
      console.log(`Starting autonomous market research for session ${request.sessionId}`);

      // Use the market research workflow
      const workflows = this.mastra.getWorkflows();
      const workflow = workflows?.marketResearchWorkflow;
      if (!workflow) {
        throw new Error('Market research workflow not found');
      }

      const run = workflow.createRun();
      const result = await run.start({
        inputData: {
          query: `Market research for ${request.targetMarket}`,
          context: {
            sessionId: request.sessionId,
            targetMarket: request.targetMarket,
            competitors: request.competitors || [],
            researchQueries: request.researchQueries,
            focusAreas: request.focusAreas || ['market-size', 'competition', 'trends']
          }
        }
      });

      // Store results in session data (if fallback manager exists)
      if (this.fallbackSessionManager) {
        await this.fallbackSessionManager.updateSessionData(request.sessionId, {
          marketResearch: result,
          timestamp: new Date().toISOString()
        });
      }

      return {
        sessionId: request.sessionId,
        marketResearch: result,
        provider: 'mastra-autonomous',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Mastra market research failed:', error);
      
      // Fallback to existing session manager if available
      if (this.fallbackSessionManager?.conductMarketResearch) {
        console.log('Falling back to standard market research');
        return await this.fallbackSessionManager.conductMarketResearch(request);
      }

      throw new Error(`Market research failed: ${error.message}`);
    }
  }

  /**
   * Validate technical feasibility using Mastra workflows
   */
  async validateTechnicalFeasibility(request: TechnicalValidationRequest): Promise<any> {
    try {
      console.log(`Starting autonomous technical validation for session ${request.sessionId}`);

      // Use the technical documentation workflow
      const workflows = this.mastra.getWorkflows();
      const workflow = workflows?.['technicalDocumentationWorkflow'];
      if (!workflow) {
        throw new Error('Technical documentation workflow not found');
      }

      const run = workflow.createRun();
      const result = await run.start({
        inputData: {
          query: `Technical feasibility analysis for ${request.projectType}`,
          taskDescription: `Validate technical feasibility for ${request.technologies.join(', ')}`,
          context: {
            sessionId: request.sessionId,
            technologies: request.technologies,
            projectType: request.projectType,
            constraints: request.constraints || [],
            features: request.features || []
          }
        }
      });

      // Store results in session data
      if (this.fallbackSessionManager) {
        await this.fallbackSessionManager.updateSessionData(request.sessionId, {
          technicalValidation: result,
          timestamp: new Date().toISOString()
        });
      }

      return {
        sessionId: request.sessionId,
        technicalValidation: result,
        provider: 'mastra-autonomous',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Mastra technical validation failed:', error);
      
      // Fallback to existing session manager if available
      if (this.fallbackSessionManager?.validateTechnicalFeasibility) {
        console.log('Falling back to standard technical validation');
        return await this.fallbackSessionManager.validateTechnicalFeasibility(request);
      }

      throw new Error(`Technical validation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Conduct UX research using Mastra workflows
   */
  async conductUXResearch(sessionId: string, targetUrls: string[], analysisType: string = 'comprehensive'): Promise<any> {
    try {
      console.log(`Starting autonomous UX research for session ${sessionId}`);

      // Use the UX research workflow
      const workflows = this.mastra.getWorkflows();
      const workflow = workflows?.['uxResearchWorkflow'];
      if (!workflow) {
        throw new Error('UX research workflow not found');
      }

      const run = workflow.createRun();
      const result = await run.start({
        inputData: {
          query: `UX research and competitive analysis`,
          context: {
            sessionId,
            targetUrls,
            analysisType,
            viewports: [
              { width: 1920, height: 1080 },
              { width: 768, height: 1024 },
              { width: 375, height: 667 }
            ]
          }
        }
      });

      // Store results in session data
      if (this.fallbackSessionManager) {
        await this.fallbackSessionManager.updateSessionData(sessionId, {
          uxResearch: result,
          timestamp: new Date().toISOString()
        });
      }

      return {
        sessionId,
        uxResearch: result,
        provider: 'mastra-autonomous',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Mastra UX research failed:', error);
      
      // Fallback to existing methods if available
      if (this.fallbackSessionManager?.conductUXResearch) {
        console.log('Falling back to standard UX research');
        return await this.fallbackSessionManager.conductUXResearch(sessionId, targetUrls, analysisType);
      }

      throw new Error(`UX research failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Synthesize requirements using autonomous research insights
   */
  async synthesizeRequirements(request: RequirementsSynthesis): Promise<any> {
    try {
      console.log(`Starting autonomous requirements synthesis for session ${request.sessionId}`);

      // Use research orchestrator for comprehensive analysis
      const workflows = this.mastra.getWorkflows();
      const workflow = workflows?.['researchOrchestrator'];
      if (!workflow) {
        throw new Error('Research orchestrator workflow not found');
      }

      const synthesisQuery = `Synthesize requirements for: ${request.problemStatement}. Target users: ${request.targetUsers.join(', ')}`;

      const run = workflow.createRun();
      const result = await run.start({
        inputData: {
          query: synthesisQuery,
          context: {
            sessionId: request.sessionId,
            userId: request.sessionId,
            priority: 'high',
            problemStatement: request.problemStatement,
            targetUsers: request.targetUsers,
            functionalRequirements: request.functionalRequirements,
            nonFunctionalRequirements: request.nonFunctionalRequirements || []
          }
        }
      });

      // Store synthesized requirements
      if (this.fallbackSessionManager) {
        await this.fallbackSessionManager.updateSessionData(request.sessionId, {
          synthesizedRequirements: result,
          timestamp: new Date().toISOString()
        });
      }

      return {
        sessionId: request.sessionId,
        synthesizedRequirements: result,
        provider: 'mastra-autonomous',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Mastra requirements synthesis failed:', error);
      
      // Fallback to existing methods if available
      if (this.fallbackSessionManager?.synthesizeRequirements) {
        console.log('Falling back to standard requirements synthesis');
        return await this.fallbackSessionManager.synthesizeRequirements(request);
      }

      throw new Error(`Requirements synthesis failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get session data with autonomous research results
   */
  async getSessionData(sessionId: string): Promise<DiscoverySession | null> {
    try {
      // Try to get session data from fallback manager first
      if (this.fallbackSessionManager?.getSessionData) {
        return await this.fallbackSessionManager.getSessionData(sessionId);
      }

      // If no fallback, return basic session structure
      return {
        sessionId,
        projectName: 'Unknown Project',
        stage: 'autonomous-research',
        data: {},
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Failed to get session data:', error);
      return null;
    }
  }

  /**
   * Update session data with autonomous research results
   */
  async updateSessionData(sessionId: string, data: any): Promise<void> {
    try {
      if (this.fallbackSessionManager?.updateSessionData) {
        await this.fallbackSessionManager.updateSessionData(sessionId, {
          ...data,
          autonomousResearchProvider: 'mastra',
          lastUpdated: new Date().toISOString()
        });
      } else {
        console.log(`Session data update for ${sessionId}:`, data);
      }
    } catch (error) {
      console.error('Failed to update session data:', error);
    }
  }
}

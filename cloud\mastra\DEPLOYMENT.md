# Guidant Mastra AI - Cloud Run Deployment Guide

This guide provides step-by-step instructions for deploying the Guidant Mastra AI Research Engine to Google Cloud Run.

## Prerequisites

### 1. Google Cloud Setup

- Google Cloud Project with billing enabled
- Google Cloud SDK (gcloud) installed and authenticated
- Docker installed and running

### 2. Required APIs

Enable the following Google Cloud APIs:

```bash
gcloud services enable \
    cloudbuild.googleapis.com \
    run.googleapis.com \
    containerregistry.googleapis.com \
    aiplatform.googleapis.com \
    firestore.googleapis.com \
    secretmanager.googleapis.com
```

### 3. Service Account Setup

Create a service account for the application:

```bash
# Create service account
gcloud iam service-accounts create guidant-mastra-sa \
    --display-name="Guidant Mastra AI Service Account"

# Grant necessary permissions
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:guidant-mastra-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:guidant-mastra-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/datastore.user"

# Create and download service account key
gcloud iam service-accounts keys create ./gcp-service-account-key.json \
    --iam-account=guidant-mastra-sa@PROJECT_ID.iam.gserviceaccount.com
```

## Environment Configuration

### 1. Create Secrets

Create Google Cloud Secret Manager secrets for sensitive configuration:

```bash
# Create secrets file
cat > secrets.json << EOF
{
  "upstash-redis-url": "your-upstash-redis-url",
  "upstash-redis-token": "your-upstash-redis-token",
  "vertex-project-id": "your-gcp-project-id",
  "tavily-api-key": "your-tavily-api-key",
  "browserbase-api-key": "your-browserbase-api-key",
  "browserbase-project-id": "your-browserbase-project-id",
  "firestore-project-id": "your-firestore-project-id"
}
EOF

# Create the secret
gcloud secrets create mastra-secrets --data-file=secrets.json

# Create service account key secret
gcloud secrets create gcp-service-account-key --data-file=gcp-service-account-key.json

# Clean up local files
rm secrets.json gcp-service-account-key.json
```

### 2. Environment Variables

Set the following environment variables:

```bash
export GOOGLE_CLOUD_PROJECT=your-project-id
export GOOGLE_CLOUD_REGION=us-central1
```

## Deployment Options

### Option 1: Automated Deployment Script

Use the provided deployment script for automated deployment:

```bash
# Make script executable
chmod +x deploy.sh

# Set environment variables
export GOOGLE_CLOUD_PROJECT=your-project-id
export GOOGLE_CLOUD_REGION=us-central1

# Run deployment
./deploy.sh
```

### Option 2: Manual Deployment

#### Step 1: Build and Push Docker Image

```bash
# Set variables
PROJECT_ID=your-project-id
SERVICE_NAME=guidant-mastra-research
IMAGE_NAME=gcr.io/${PROJECT_ID}/${SERVICE_NAME}

# Build image
docker build -t ${IMAGE_NAME}:latest .

# Push to Google Container Registry
docker push ${IMAGE_NAME}:latest
```

#### Step 2: Deploy to Cloud Run

```bash
gcloud run deploy guidant-mastra-research \
    --image gcr.io/${PROJECT_ID}/guidant-mastra-research:latest \
    --region us-central1 \
    --platform managed \
    --allow-unauthenticated \
    --service-account guidant-mastra-sa@${PROJECT_ID}.iam.gserviceaccount.com \
    --set-env-vars NODE_ENV=production \
    --set-env-vars PORT=8080 \
    --set-env-vars VERTEX_LOCATION=us-central1 \
    --set-secrets UPSTASH_REDIS_REST_URL=mastra-secrets:latest:upstash-redis-url \
    --set-secrets UPSTASH_REDIS_REST_TOKEN=mastra-secrets:latest:upstash-redis-token \
    --set-secrets VERTEX_PROJECT_ID=mastra-secrets:latest:vertex-project-id \
    --set-secrets TAVILY_API_KEY=mastra-secrets:latest:tavily-api-key \
    --set-secrets BROWSERBASE_API_KEY=mastra-secrets:latest:browserbase-api-key \
    --set-secrets BROWSERBASE_PROJECT_ID=mastra-secrets:latest:browserbase-project-id \
    --set-secrets FIRESTORE_PROJECT_ID=mastra-secrets:latest:firestore-project-id \
    --set-secrets GOOGLE_APPLICATION_CREDENTIALS=gcp-service-account-key:latest \
    --memory 4Gi \
    --cpu 2 \
    --timeout 300s \
    --concurrency 100 \
    --min-instances 0 \
    --max-instances 10
```

### Option 3: YAML Configuration Deployment

Use the provided `cloud-run.yaml` configuration:

```bash
# Update PROJECT_ID in cloud-run.yaml
sed -i 's/PROJECT_ID/your-actual-project-id/g' cloud-run.yaml

# Deploy using YAML
gcloud run services replace cloud-run.yaml --region us-central1
```

## Post-Deployment Verification

### 1. Health Check

```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe guidant-mastra-research \
    --region us-central1 --format='value(status.url)')

# Test health endpoint
curl ${SERVICE_URL}/health

# Test Guidant integration health
curl ${SERVICE_URL}/guidant/health
```

### 2. Test Research Endpoint

```bash
curl -X POST ${SERVICE_URL}/guidant/research \
    -H "Content-Type: application/json" \
    -d '{
        "query": "Test autonomous research query",
        "context": {
            "sessionId": "test-session-123",
            "priority": "medium"
        }
    }'
```

## Monitoring and Logging

### 1. View Logs

```bash
# View recent logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=guidant-mastra-research" \
    --limit 50 --format json

# Follow logs in real-time
gcloud logs tail "resource.type=cloud_run_revision AND resource.labels.service_name=guidant-mastra-research"
```

### 2. Monitoring Dashboard

Access Cloud Run monitoring in the Google Cloud Console:
- Navigate to Cloud Run > guidant-mastra-research
- View metrics, logs, and performance data

## Scaling Configuration

The service is configured with:
- **Min instances**: 0 (scales to zero when not in use)
- **Max instances**: 10 (auto-scales based on demand)
- **Concurrency**: 100 requests per instance
- **CPU**: 2 vCPU per instance
- **Memory**: 4 GB per instance
- **Timeout**: 300 seconds (5 minutes)

### Adjust Scaling

```bash
gcloud run services update guidant-mastra-research \
    --region us-central1 \
    --min-instances 1 \
    --max-instances 20 \
    --concurrency 50
```

## Security Configuration

### 1. IAM and Authentication

The service uses:
- Service account with minimal required permissions
- Secret Manager for sensitive configuration
- No public access to secrets or internal APIs

### 2. Network Security

- HTTPS-only traffic
- CORS configured for specific origins
- Helmet.js for security headers
- Request size limits

## Troubleshooting

### Common Issues

1. **Deployment Fails**
   - Check Docker image builds successfully locally
   - Verify all required APIs are enabled
   - Ensure service account has proper permissions

2. **Service Starts but Health Check Fails**
   - Check environment variables and secrets
   - Verify Redis/Upstash connectivity
   - Review application logs

3. **MCP Server Connection Issues**
   - Verify API keys in Secret Manager
   - Check network connectivity
   - Review MCP server logs

### Debug Commands

```bash
# Check service status
gcloud run services describe guidant-mastra-research --region us-central1

# View recent deployments
gcloud run revisions list --service guidant-mastra-research --region us-central1

# Check secrets
gcloud secrets versions list mastra-secrets
```

## Updating the Service

### 1. Update Code and Redeploy

```bash
# Build new image with updated tag
docker build -t gcr.io/${PROJECT_ID}/guidant-mastra-research:v2 .
docker push gcr.io/${PROJECT_ID}/guidant-mastra-research:v2

# Update service
gcloud run services update guidant-mastra-research \
    --image gcr.io/${PROJECT_ID}/guidant-mastra-research:v2 \
    --region us-central1
```

### 2. Update Configuration

```bash
# Update environment variables
gcloud run services update guidant-mastra-research \
    --region us-central1 \
    --set-env-vars NEW_VAR=value

# Update secrets
echo "new-secret-value" | gcloud secrets versions add secret-name --data-file=-
```

## Cost Optimization

- Service scales to zero when not in use
- Use appropriate instance sizing
- Monitor usage patterns and adjust scaling
- Consider regional deployment for reduced latency

## Support

For deployment issues:
1. Check the troubleshooting section
2. Review Google Cloud Run documentation
3. Check Mastra AI documentation
4. Contact the development team

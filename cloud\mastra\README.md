# Guidant Mastra AI Research Engine

Autonomous research agents for Guidant using Mastra AI workflows, deployed on Google Cloud Run.

## Overview

This implementation provides specialized research capabilities that integrate seamlessly with Guidant's existing ResearchRouter and DiscoverySessionManager systems. The TypeScript-first, cloud-native architecture leverages Mastra's workflow orchestration to deliver robust, scalable research automation.

## Architecture

### Core Components

- **Research Orchestrator**: Central workflow that routes queries to specialized research workflows
- **Technology Research**: Context7 MCP integration for technical documentation research
- **Market Research**: Tavily MCP integration for market analysis and competitive research
- **UX Research**: Stagehand MCP integration for browser automation and UI/UX analysis
- **Guidant Integration**: Adapters for seamless integration with existing Guidant systems

### MCP Server Integrations

- **Context7**: Technical documentation and library research
- **Tavily**: Market research and web search capabilities
- **Stagehand**: Browser automation for UX research and competitive analysis

## Quick Start

### Prerequisites

- Node.js 18+
- Google Cloud Project with required APIs enabled
- Required API keys (see Environment Variables section)

### Installation

```bash
# Install dependencies
npm install

# Build the project
npm run build

# Start development server
npm run dev
```

### Environment Variables

Create a `.env` file with the following variables:

```env
# Server Configuration
NODE_ENV=development
PORT=8080
HOST=0.0.0.0

# Redis/Upstash Configuration
UPSTASH_REDIS_REST_URL=your-upstash-redis-url
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token

# Vertex AI Configuration
VERTEX_PROJECT_ID=your-gcp-project-id
VERTEX_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# MCP Server API Keys
TAVILY_API_KEY=your-tavily-api-key
BROWSERBASE_API_KEY=your-browserbase-api-key
BROWSERBASE_PROJECT_ID=your-browserbase-project-id

# Firestore Configuration
FIRESTORE_PROJECT_ID=your-firestore-project-id
```

## API Endpoints

### Core Research

- `POST /research` - Main research endpoint using orchestrator workflow
- `POST /workflows/:workflowName/execute` - Direct workflow execution
- `GET /workflows` - List available workflows
- `GET /health` - Service health check

### Guidant Integration

- `POST /guidant/research` - Enhanced research using Guidant integration
- `POST /guidant/market-research` - Market research for discovery sessions
- `POST /guidant/technical-validation` - Technical feasibility validation
- `POST /guidant/ux-research` - UX research and competitive analysis
- `GET /guidant/health` - Integration health check

### Example Request

```bash
curl -X POST http://localhost:8080/guidant/research \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Analyze the technical feasibility of implementing real-time collaboration features",
    "context": {
      "sessionId": "discovery-session-123",
      "technologies": ["React", "WebSocket", "Node.js"],
      "projectType": "web-application"
    }
  }'
```

## Deployment

### Local Development

```bash
# Start development server with hot reload
npm run dev

# Run tests
npm test

# Lint code
npm run lint
```

### Docker Deployment

```bash
# Build Docker image
npm run docker:build

# Run Docker container
npm run docker:run
```

### Google Cloud Run Deployment

```bash
# Deploy using the deployment script
npm run deploy

# Or deploy using gcloud directly
npm run deploy:gcloud
```

The deployment script will:
1. Build and push Docker image to Google Container Registry
2. Deploy to Cloud Run with proper configuration
3. Set up required secrets and environment variables

## Integration with Existing Guidant

### ResearchRouter Integration

The `MastraResearchRouterAdapter` extends Guidant's existing ResearchRouter to support autonomous research workflows while maintaining backward compatibility.

```typescript
import { createGuidantIntegration } from './integration';

// Initialize integration
const integration = await createGuidantIntegration(mastra);

// Use enhanced research routing
const result = await integration.executeResearch(query, context);
```

### DiscoverySessionManager Integration

The `MastraDiscoverySessionAdapter` integrates with Guidant's discovery session workflow to provide autonomous research capabilities.

```typescript
// Conduct market research
const marketResearch = await integration.conductMarketResearch(
  sessionId,
  researchQueries,
  targetMarket
);

// Validate technical feasibility
const technicalValidation = await integration.validateTechnicalFeasibility(
  sessionId,
  technologies,
  projectType
);
```

## Monitoring and Observability

### Health Checks

- `/health` - Basic service health
- `/guidant/health` - Integration-specific health with workflow and MCP server status

### Logging

Structured JSON logging using Pino logger with correlation IDs for request tracing.

### Metrics

- Request duration and success rates
- Workflow execution metrics
- MCP server availability and response times

## Development

### Project Structure

```
src/
├── workflows/          # Mastra AI workflows
│   ├── orchestrator.ts # Main orchestrator workflow
│   ├── technology.ts   # Technical research workflow
│   ├── market.ts       # Market research workflow
│   └── ux.ts          # UX research workflow
├── integration/        # Guidant integration adapters
│   ├── research-router-adapter.ts
│   ├── discovery-session-adapter.ts
│   └── index.ts
├── utils/             # Utility modules
│   ├── config.ts      # Configuration management
│   └── logger.ts      # Logging utilities
├── server.ts          # Express server setup
└── index.ts           # Main application entry point
```

### Adding New Workflows

1. Create workflow file in `src/workflows/`
2. Register workflow in `src/index.ts`
3. Add integration endpoints in `src/server.ts`
4. Update integration adapters as needed

## Troubleshooting

### Common Issues

1. **MCP Server Connection Failures**
   - Verify API keys are correctly set
   - Check network connectivity
   - Review MCP server logs

2. **Workflow Execution Errors**
   - Check input data validation
   - Verify workflow registration
   - Review Mastra logs

3. **Integration Issues**
   - Ensure Guidant systems are accessible
   - Verify session data format
   - Check fallback mechanisms

### Debug Mode

Set `NODE_ENV=development` and `LOG_LEVEL=debug` for detailed logging.

## Contributing

1. Follow TypeScript best practices
2. Add tests for new functionality
3. Update documentation
4. Ensure proper error handling and logging

## License

MIT License - see LICENSE file for details.

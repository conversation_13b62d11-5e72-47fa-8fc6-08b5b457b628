{"name": "guidant-mastra-agents", "version": "1.0.0", "description": "Autonomous research agents for Guidant using Mastra AI workflows", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "docker:build": "docker build -t guidant-mastra-agents .", "docker:run": "docker run -p 8080:8080 guidant-mastra-agents", "deploy": "./deploy.sh", "deploy:dev": "GOOGLE_CLOUD_REGION=us-central1 ./deploy.sh", "deploy:gcloud": "gcloud run deploy guidant-mastra-research --source . --region us-central1"}, "dependencies": {"@mastra/core": "^0.10.7", "@mastra/mcp": "^0.10.5", "@mastra/loggers": "^0.10.2", "@ai-sdk/google-vertex": "^0.0.9", "firebase": "^10.7.1", "zod": "^3.22.4", "express": "^4.19.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "dotenv": "^16.4.5", "winston": "^3.11.0", "redis": "^4.6.12"}, "devDependencies": {"@types/node": "^20.14.9", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "typescript": "^5.5.3", "tsx": "^4.16.2", "vitest": "^2.0.3", "@vitest/coverage-v8": "^2.0.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mastra", "ai", "agents", "workflows", "research", "guidant", "typescript"], "author": "Guidant Team", "license": "MIT"}
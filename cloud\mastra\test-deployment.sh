#!/bin/bash

# Guidant Mastra AI - Deployment Testing Script
# This script tests the deployed service to ensure all endpoints are working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="guidant-mastra-research"
REGION="${GOOGLE_CLOUD_REGION:-us-central1}"
PROJECT_ID="${GOOGLE_CLOUD_PROJECT}"

# Get service URL
echo -e "${BLUE}Getting service URL...${NC}"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME \
    --region $REGION \
    --format='value(status.url)' 2>/dev/null || echo "")

if [ -z "$SERVICE_URL" ]; then
    echo -e "${RED}❌ Could not get service URL. Is the service deployed?${NC}"
    echo "Run: gcloud run services list --region $REGION"
    exit 1
fi

echo -e "${GREEN}✅ Service URL: $SERVICE_URL${NC}"

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$SERVICE_URL$endpoint" || echo "000")
    else
        response=$(curl -s -w "\n%{http_code}" -X $method \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$SERVICE_URL$endpoint" || echo "000")
    fi
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ Status: $status_code (Expected: $expected_status)${NC}"
        if [ ${#body} -gt 200 ]; then
            echo "Response: $(echo "$body" | head -c 200)..."
        else
            echo "Response: $body"
        fi
    else
        echo -e "${RED}❌ Status: $status_code (Expected: $expected_status)${NC}"
        echo "Response: $body"
        return 1
    fi
}

# Test suite
echo -e "\n${BLUE}🧪 Starting deployment tests...${NC}"

# Test 1: Health check
test_endpoint "GET" "/health" "" "200" "Basic health check"

# Test 2: Guidant integration health
test_endpoint "GET" "/guidant/health" "" "200" "Guidant integration health check"

# Test 3: List workflows
test_endpoint "GET" "/workflows" "" "200" "List available workflows"

# Test 4: Basic research endpoint
research_data='{
    "query": "Test research query for deployment verification",
    "context": {
        "sessionId": "test-deployment-session",
        "queryType": "general"
    }
}'
test_endpoint "POST" "/research" "$research_data" "200" "Basic research endpoint"

# Test 5: Guidant research endpoint
guidant_research_data='{
    "query": "Test Guidant integration research",
    "context": {
        "sessionId": "test-guidant-session",
        "priority": "medium"
    }
}'
test_endpoint "POST" "/guidant/research" "$guidant_research_data" "200" "Guidant research endpoint"

# Test 6: Technology workflow direct execution
tech_workflow_data='{
    "inputData": {
        "query": "React framework analysis",
        "technologies": ["React", "TypeScript"],
        "context": {
            "sessionId": "test-tech-session"
        }
    }
}'
test_endpoint "POST" "/workflows/technologyResearch/execute" "$tech_workflow_data" "200" "Technology workflow execution"

# Test 7: Market research endpoint
market_research_data='{
    "sessionId": "test-market-session",
    "researchQueries": ["Test market analysis"],
    "targetMarket": "Software Development Tools",
    "competitors": ["GitHub", "GitLab"]
}'
test_endpoint "POST" "/guidant/market-research" "$market_research_data" "200" "Market research endpoint"

# Test 8: Technical validation endpoint
tech_validation_data='{
    "sessionId": "test-validation-session",
    "technologies": ["React", "Node.js", "PostgreSQL"],
    "projectType": "web-application",
    "constraints": ["Must support real-time updates"],
    "features": ["User authentication", "Data visualization"]
}'
test_endpoint "POST" "/guidant/technical-validation" "$tech_validation_data" "200" "Technical validation endpoint"

# Test 9: UX research endpoint
ux_research_data='{
    "sessionId": "test-ux-session",
    "targetUrls": ["https://github.com", "https://gitlab.com"],
    "analysisType": "comprehensive"
}'
test_endpoint "POST" "/guidant/ux-research" "$ux_research_data" "200" "UX research endpoint"

# Test 10: Error handling - invalid endpoint
test_endpoint "GET" "/invalid-endpoint" "" "404" "404 error handling"

# Test 11: Error handling - invalid JSON
test_endpoint "POST" "/research" "invalid-json" "400" "Invalid JSON handling"

echo -e "\n${BLUE}📊 Performance and Load Testing${NC}"

# Simple load test
echo -e "\n${YELLOW}Running basic load test (10 concurrent requests)...${NC}"
for i in {1..10}; do
    (curl -s -o /dev/null -w "%{http_code} %{time_total}s\n" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$research_data" \
        "$SERVICE_URL/research") &
done
wait

echo -e "\n${BLUE}🔍 Service Information${NC}"

# Get service details
echo -e "\n${YELLOW}Service Configuration:${NC}"
gcloud run services describe $SERVICE_NAME --region $REGION --format="table(
    metadata.name,
    status.url,
    spec.template.spec.containers[0].image,
    spec.template.spec.containers[0].resources.limits.memory,
    spec.template.spec.containers[0].resources.limits.cpu,
    spec.template.metadata.annotations['autoscaling.knative.dev/maxScale'],
    spec.template.metadata.annotations['autoscaling.knative.dev/minScale']
)"

# Check recent logs
echo -e "\n${YELLOW}Recent Logs (last 10 entries):${NC}"
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE_NAME" \
    --limit 10 \
    --format "table(timestamp,severity,textPayload)" \
    --region $REGION

# Check metrics
echo -e "\n${YELLOW}Service Metrics:${NC}"
echo "View detailed metrics at:"
echo "https://console.cloud.google.com/run/detail/$REGION/$SERVICE_NAME/metrics?project=$PROJECT_ID"

echo -e "\n${GREEN}🎉 Deployment testing completed!${NC}"
echo -e "\n${BLUE}Service Endpoints:${NC}"
echo "• Health Check: $SERVICE_URL/health"
echo "• Guidant Health: $SERVICE_URL/guidant/health"
echo "• Research API: $SERVICE_URL/research"
echo "• Guidant Research: $SERVICE_URL/guidant/research"
echo "• Workflows: $SERVICE_URL/workflows"

echo -e "\n${BLUE}Next Steps:${NC}"
echo "1. Monitor service logs: gcloud logs tail \"resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE_NAME\""
echo "2. View metrics in Cloud Console"
echo "3. Set up monitoring alerts"
echo "4. Configure custom domain (if needed)"
echo "5. Update Guidant configuration to use this service"

echo -e "\n${YELLOW}Integration with Guidant:${NC}"
echo "Add this service URL to your Guidant configuration:"
echo "MASTRA_SERVICE_URL=$SERVICE_URL"

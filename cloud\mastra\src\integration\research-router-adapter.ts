/**
 * Research Router Adapter for Mastra AI Integration
 * 
 * Extends Guidant's existing ResearchRouter to support autonomous research
 * workflows while maintaining backward compatibility with existing providers.
 */

import { Mastra } from '@mastra/core';
import { z } from 'zod';

// Types for integration with existing ResearchRouter
interface ResearchContext {
  sessionId?: string;
  stage?: string;
  projectType?: string;
  targetMarket?: string;
  competitors?: string[];
  technologies?: string[];
  features?: string[];
  priority?: 'low' | 'medium' | 'high';
}

interface ResearchResult {
  provider: string;
  queryType: string;
  query: string;
  results: any;
  metadata: {
    routingDecision: string;
    timestamp: string;
    context: ResearchContext;
  };
}

// Query types from existing ResearchRouter
const RESEARCH_QUERY_TYPES = {
  TECHNICAL: 'technical',
  MARKET: 'market',
  COMPETITIVE: 'competitive',
  UX: 'ux',
  HYBRID: 'hybrid',
  GENERAL: 'general'
};

// Provider types
const RESEARCH_PROVIDERS = {
  MASTRA_AUTONOMOUS: 'mastra-autonomous',
  CONTEXT7: 'context7',
  TAVILY: 'tavily',
  PERPLEXITY: 'perplexity'
};

export class MastraResearchRouterAdapter {
  private mastra: Mastra;
  private fallbackRouter?: any; // Original ResearchRouter instance

  constructor(mastra: Mastra, fallbackRouter?: any) {
    this.mastra = mastra;
    this.fallbackRouter = fallbackRouter;
  }

  /**
   * Enhanced route query that supports autonomous research workflows
   */
  async routeQuery(query: string, context: ResearchContext = {}): Promise<ResearchResult> {
    try {
      // Check if autonomous research is needed
      if (this.shouldUseAutonomousResearch(query, context)) {
        return await this.routeToMastraWorkflow(query, context);
      }

      // Fallback to existing ResearchRouter if available
      if (this.fallbackRouter) {
        return await this.fallbackRouter.routeQuery(query, context);
      }

      // If no fallback, use basic Mastra routing
      return await this.routeToMastraWorkflow(query, context);

    } catch (error) {
      console.error('Research routing failed:', error);
      
      // Try fallback router if Mastra fails
      if (this.fallbackRouter) {
        console.log('Falling back to standard research providers');
        return await this.fallbackRouter.routeQuery(query, context);
      }

      throw new Error(`Research routing failed: ${error.message}`);
    }
  }

  /**
   * Route query to appropriate Mastra workflow
   */
  private async routeToMastraWorkflow(query: string, context: ResearchContext): Promise<ResearchResult> {
    const queryType = this.classifyQuery(query, context);
    
    console.log(`Routing query "${query}" to Mastra workflow (type: ${queryType})`);

    // Use the research orchestrator workflow
    const workflow = this.mastra.workflows.researchOrchestrator;
    if (!workflow) {
      throw new Error('Research orchestrator workflow not found');
    }

    const run = workflow.createRun();
    const result = await run.start({
      inputData: {
        query,
        context: {
          sessionId: context.sessionId || this.generateSessionId(),
          userId: context.sessionId, // Use sessionId as userId for now
          priority: context.priority || 'medium'
        }
      }
    });

    return {
      provider: RESEARCH_PROVIDERS.MASTRA_AUTONOMOUS,
      queryType,
      query,
      results: result,
      metadata: {
        routingDecision: `Routed to Mastra ${queryType} workflow`,
        timestamp: new Date().toISOString(),
        context
      }
    };
  }

  /**
   * Determine if query should use autonomous research workflows
   */
  private shouldUseAutonomousResearch(query: string, context: ResearchContext): boolean {
    // Use autonomous research for complex queries or specific contexts
    const complexityIndicators = [
      'comprehensive analysis',
      'detailed research',
      'competitive landscape',
      'market opportunity',
      'technical feasibility',
      'architecture recommendations',
      'implementation strategy'
    ];

    const queryLower = query.toLowerCase();
    const hasComplexityIndicator = complexityIndicators.some(indicator => 
      queryLower.includes(indicator)
    );

    // Use autonomous research if:
    // 1. Query indicates complexity
    // 2. Context suggests discovery stage research
    // 3. Multiple research areas are involved
    return hasComplexityIndicator || 
           context.stage === 'market-research' ||
           context.stage === 'technical-validation' ||
           (context.technologies && context.technologies.length > 1) ||
           (context.competitors && context.competitors.length > 0);
  }

  /**
   * Classify query type (compatible with existing ResearchRouter)
   */
  private classifyQuery(query: string, context: ResearchContext): string {
    const queryLower = query.toLowerCase();

    // Technical indicators
    const technicalKeywords = [
      'api', 'framework', 'library', 'architecture', 'implementation',
      'technical', 'code', 'development', 'programming', 'software'
    ];

    // Market indicators  
    const marketKeywords = [
      'market', 'competitor', 'business', 'industry', 'pricing',
      'opportunity', 'revenue', 'customer', 'segment'
    ];

    // UX indicators
    const uxKeywords = [
      'ux', 'ui', 'user', 'design', 'interface', 'experience',
      'usability', 'accessibility', 'interaction'
    ];

    const technicalMatches = technicalKeywords.filter(keyword => 
      queryLower.includes(keyword)
    ).length;

    const marketMatches = marketKeywords.filter(keyword => 
      queryLower.includes(keyword)
    ).length;

    const uxMatches = uxKeywords.filter(keyword => 
      queryLower.includes(keyword)
    ).length;

    // Determine primary type
    if (technicalMatches > marketMatches && technicalMatches > uxMatches) {
      return RESEARCH_QUERY_TYPES.TECHNICAL;
    }

    if (marketMatches > technicalMatches && marketMatches > uxMatches) {
      return RESEARCH_QUERY_TYPES.MARKET;
    }

    if (uxMatches > technicalMatches && uxMatches > marketMatches) {
      return RESEARCH_QUERY_TYPES.UX;
    }

    // Check for hybrid queries
    if (technicalMatches > 0 && marketMatches > 0) {
      return RESEARCH_QUERY_TYPES.HYBRID;
    }

    // Default to general
    return RESEARCH_QUERY_TYPES.GENERAL;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `mastra_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Batch query routing with load balancing
   */
  async routeBatch(queries: Array<{query: string, context?: ResearchContext}>): Promise<ResearchResult[]> {
    console.log(`Routing batch of ${queries.length} queries`);

    // Process queries in parallel with concurrency limit
    const concurrencyLimit = 3;
    const results: ResearchResult[] = [];

    for (let i = 0; i < queries.length; i += concurrencyLimit) {
      const batch = queries.slice(i, i + concurrencyLimit);
      const batchPromises = batch.map(({ query, context }) => 
        this.routeQuery(query, context || {})
      );

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`Batch query ${i + index} failed:`, result.reason);
          // Add error result
          results.push({
            provider: 'error',
            queryType: 'error',
            query: batch[index].query,
            results: { error: result.reason.message },
            metadata: {
              routingDecision: 'Failed to route query',
              timestamp: new Date().toISOString(),
              context: batch[index].context || {}
            }
          });
        }
      });
    }

    return results;
  }
}

/**
 * Configuration management for Guidant Mastra AI Research Engine
 */

import { z } from 'zod';

// Configuration schema validation
const ConfigSchema = z.object({
  server: z.object({
    port: z.number().default(8080),
    host: z.string().default('0.0.0.0'),
    cors: z.object({
      origin: z.union([z.string(), z.array(z.string())]).default('*'),
      credentials: z.boolean().default(true)
    }).default({})
  }).default({}),
  
  redis: z.object({
    url: z.string(),
    token: z.string(),
    db: z.number().default(0)
  }),
  
  vertexAI: z.object({
    projectId: z.string(),
    location: z.string().default('us-central1'),
    models: z.object({
      primary: z.string().default('gemini-2.5-pro'),
      fallback: z.string().default('gemini-2.5-flash')
    }).default({})
  }),
  
  context7: z.object({
    apiKey: z.string().optional(),
    baseUrl: z.string().optional()
  }).default({}),
  
  tavily: z.object({
    apiKey: z.string()
  }),
  
  stagehand: z.object({
    apiKey: z.string(),
    projectId: z.string()
  }),
  
  guidant: z.object({
    apiUrl: z.string().optional(),
    firestoreProject: z.string()
  }),
  
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
    format: z.enum(['json', 'pretty']).default('json')
  }).default({})
});

export type Config = z.infer<typeof ConfigSchema>;

/**
 * Load and validate configuration from environment variables
 */
export function loadConfig(): Config {
  const config = {
    server: {
      port: parseInt(process.env['PORT'] || '8080', 10),
      host: process.env['HOST'] || '0.0.0.0',
      cors: {
        origin: process.env['CORS_ORIGIN'] || '*',
        credentials: process.env['CORS_CREDENTIALS'] === 'true'
      }
    },
    
    redis: {
      url: process.env['REDIS_URL'] || process.env['UPSTASH_REDIS_REST_URL'],
      token: process.env['REDIS_TOKEN'] || process.env['UPSTASH_REDIS_REST_TOKEN'],
      db: parseInt(process.env['REDIS_DB'] || '0', 10)
    },
    
    vertexAI: {
      projectId: process.env['VERTEX_PROJECT_ID'] || process.env['GOOGLE_CLOUD_PROJECT'],
      location: process.env['VERTEX_LOCATION'] || 'us-central1',
      models: {
        primary: process.env['VERTEX_PRIMARY_MODEL'] || 'gemini-2.5-pro',
        fallback: process.env['VERTEX_FALLBACK_MODEL'] || 'gemini-2.5-flash'
      }
    },
    
    context7: {
      apiKey: process.env['CONTEXT7_API_KEY'],
      baseUrl: process.env['CONTEXT7_BASE_URL']
    },

    tavily: {
      apiKey: process.env['TAVILY_API_KEY']
    },

    stagehand: {
      apiKey: process.env['BROWSERBASE_API_KEY'],
      projectId: process.env['BROWSERBASE_PROJECT_ID']
    },

    guidant: {
      apiUrl: process.env['GUIDANT_API_URL'],
      firestoreProject: process.env['FIRESTORE_PROJECT_ID'] || process.env['GOOGLE_CLOUD_PROJECT']
    },

    logging: {
      level: (process.env['LOG_LEVEL'] as any) || 'info',
      format: (process.env['LOG_FORMAT'] as any) || 'json'
    }
  };

  try {
    return ConfigSchema.parse(config);
  } catch (error) {
    console.error('Configuration validation failed:', error);
    throw new Error('Invalid configuration. Please check your environment variables.');
  }
}

/**
 * Get required environment variables with validation
 */
export function getRequiredEnv(key: string): string {
  const value = process.env[key];
  if (!value) {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  return value;
}

/**
 * Get optional environment variable with default
 */
export function getOptionalEnv(key: string, defaultValue: string): string {
  return process.env[key] || defaultValue;
}

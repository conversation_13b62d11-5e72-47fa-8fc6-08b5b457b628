apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: guidant-mastra-research
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Auto-scaling configuration
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        
        # Resource allocation
        run.googleapis.com/cpu: "2"
        run.googleapis.com/memory: "4Gi"
        
        # Timeout configuration
        run.googleapis.com/timeout: "300s"
        
        # VPC connector (if needed for private resources)
        # run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/REGION/connectors/CONNECTOR_NAME
        
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - name: mastra-research
        image: gcr.io/PROJECT_ID/guidant-mastra-research:latest
        ports:
        - name: http1
          containerPort: 8080
        
        # Environment variables
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8080"
        - name: HOST
          value: "0.0.0.0"
        
        # Redis/Upstash configuration
        - name: UPSTASH_REDIS_REST_URL
          valueFrom:
            secretKeyRef:
              name: mastra-secrets
              key: upstash-redis-url
        - name: UPSTASH_REDIS_REST_TOKEN
          valueFrom:
            secretKeyRef:
              name: mastra-secrets
              key: upstash-redis-token
        
        # Vertex AI configuration
        - name: VERTEX_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: mastra-secrets
              key: vertex-project-id
        - name: VERTEX_LOCATION
          value: "us-central1"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/secrets/gcp-key/key.json"
        
        # MCP Server API keys
        - name: TAVILY_API_KEY
          valueFrom:
            secretKeyRef:
              name: mastra-secrets
              key: tavily-api-key
        - name: BROWSERBASE_API_KEY
          valueFrom:
            secretKeyRef:
              name: mastra-secrets
              key: browserbase-api-key
        - name: BROWSERBASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: mastra-secrets
              key: browserbase-project-id
        
        # Firestore configuration
        - name: FIRESTORE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: mastra-secrets
              key: firestore-project-id
        
        # Logging configuration
        - name: LOG_LEVEL
          value: "info"
        - name: LOG_FORMAT
          value: "json"
        
        # Resource limits
        resources:
          limits:
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        
        # Volume mounts for GCP credentials
        volumeMounts:
        - name: gcp-key
          mountPath: /secrets/gcp-key
          readOnly: true
      
      # Volumes
      volumes:
      - name: gcp-key
        secret:
          secretName: gcp-service-account-key

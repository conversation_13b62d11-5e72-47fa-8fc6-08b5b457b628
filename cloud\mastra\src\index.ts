/**
 * Guidant Mastra AI Research Engine
 *
 * Single workflow engine that orchestrates autonomous research tasks
 * using specialized internal workflows and external MCP servers.
 */

import { Mastra } from '@mastra/core';
import { PinoLogger } from '@mastra/loggers';
import { createServer } from './server';
import { researchOrchestrator } from './workflows/orchestrator';
import { technicalDocumentationWorkflow } from './workflows/technology';
import { marketResearchWorkflow } from './workflows/market';
import { uxResearchWorkflow } from './workflows/ux';
import { loadConfig } from './utils/config';

// Create Mastra instance for export
const config = loadConfig();
const mastra = new Mastra({
  workflows: {
    researchOrchestrator,
    technicalDocumentationWorkflow,
    marketResearchWorkflow,
    uxResearchWorkflow
  },
  logger: new PinoLogger({
    name: 'Mastra Research',
    level: 'info'
  })
});

async function main() {
  console.log('Initializing Guidant Mastra AI Research Engine...');

  try {
    // Create and start the server
    const server = createServer(mastra, config);
    const port = config.server.port || 8080;

    server.listen(port, () => {
      console.log(`Guidant Mastra AI Research Engine started on port ${port}`);
      const workflows = mastra.getWorkflows();
      const mcpServers = mastra.getMCPServers();
      console.log('Available workflows:', Object.keys(workflows || {}));
      console.log('Available MCP servers:', Object.keys(mcpServers || {}));
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      console.log('Received SIGTERM, shutting down gracefully...');
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      console.log('Received SIGINT, shutting down gracefully...');
      process.exit(0);
    });

  } catch (error) {
    console.error('Failed to initialize Mastra AI Research Engine:', error);
    process.exit(1);
  }
}

// Start the application
main().catch((error) => {
  console.error('Unhandled error during startup:', error);
  process.exit(1);
});

// Export the mastra instance for external use
export { mastra as default };

// Export integration utilities
export { createGuidantIntegration, GuidantMastraIntegration } from './integration';

// Export types for use in other modules
export type { Config } from './utils/config';

# Guidant Mastra AI - Production Environment Configuration Template
# Copy this file to .env.production and fill in the actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=8080
HOST=0.0.0.0

# CORS Configuration
CORS_ORIGIN=https://your-domain.com,https://your-admin-domain.com
CORS_CREDENTIALS=true

# =============================================================================
# REDIS/UPSTASH CONFIGURATION
# =============================================================================
# Get these from your Upstash Redis dashboard
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token
REDIS_DB=0

# =============================================================================
# GOOGLE CLOUD / VERTEX AI CONFIGURATION
# =============================================================================
# Your Google Cloud Project ID
VERTEX_PROJECT_ID=your-gcp-project-id
GOOGLE_CLOUD_PROJECT=your-gcp-project-id

# Vertex AI Location (us-central1, us-east1, etc.)
VERTEX_LOCATION=us-central1

# Vertex AI Models
VERTEX_PRIMARY_MODEL=gemini-2.5-pro
VERTEX_FALLBACK_MODEL=gemini-2.5-flash

# Service Account Key Path (for local development)
# In Cloud Run, this is handled by Secret Manager
GOOGLE_APPLICATION_CREDENTIALS=/secrets/gcp-key/key.json

# =============================================================================
# MCP SERVER API KEYS
# =============================================================================

# Tavily API Key for market research
# Get from: https://tavily.com/
TAVILY_API_KEY=your-tavily-api-key

# Browserbase API Keys for browser automation
# Get from: https://browserbase.com/
BROWSERBASE_API_KEY=your-browserbase-api-key
BROWSERBASE_PROJECT_ID=your-browserbase-project-id

# Context7 API Configuration (if required)
CONTEXT7_API_KEY=your-context7-api-key
CONTEXT7_BASE_URL=https://api.context7.com

# =============================================================================
# FIRESTORE CONFIGURATION
# =============================================================================
# Firestore Project ID (usually same as GOOGLE_CLOUD_PROJECT)
FIRESTORE_PROJECT_ID=your-firestore-project-id

# =============================================================================
# GUIDANT INTEGRATION
# =============================================================================
# Guidant API URL (if applicable)
GUIDANT_API_URL=https://your-guidant-api.com

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level: debug, info, warn, error
LOG_LEVEL=info

# Log format: json, pretty
LOG_FORMAT=json

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret (if using authentication)
JWT_SECRET=your-jwt-secret-key

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================
# Application Insights or other monitoring
MONITORING_ENABLED=true
MONITORING_SAMPLE_RATE=0.1

# Health check configuration
HEALTH_CHECK_TIMEOUT=5000

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Request timeout in milliseconds
REQUEST_TIMEOUT=300000

# Maximum request body size
MAX_REQUEST_SIZE=10mb

# Workflow execution timeout
WORKFLOW_TIMEOUT=300000

# MCP Server timeout
MCP_SERVER_TIMEOUT=30000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable specific features
ENABLE_BATCH_PROCESSING=true
ENABLE_CACHING=true
ENABLE_METRICS=true
ENABLE_DEBUG_ENDPOINTS=false

# =============================================================================
# CLOUD RUN SPECIFIC
# =============================================================================
# These are automatically set by Cloud Run, but can be overridden
K_SERVICE=guidant-mastra-research
K_REVISION=guidant-mastra-research-00001-abc
K_CONFIGURATION=guidant-mastra-research

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================
# Backup configuration (if applicable)
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# =============================================================================
# NOTES
# =============================================================================
# 1. Never commit this file with actual secrets to version control
# 2. Use Google Cloud Secret Manager for production secrets
# 3. Validate all URLs and API keys before deployment
# 4. Monitor logs for any configuration errors
# 5. Test all integrations in staging environment first
#
# For Cloud Run deployment, most of these values should be stored in
# Google Cloud Secret Manager and referenced in the deployment configuration.
#
# Example Secret Manager setup:
# gcloud secrets create mastra-secrets --data-file=production-secrets.json
#
# Where production-secrets.json contains:
# {
#   "upstash-redis-url": "actual-redis-url",
#   "upstash-redis-token": "actual-redis-token",
#   "tavily-api-key": "actual-tavily-key",
#   "browserbase-api-key": "actual-browserbase-key",
#   "browserbase-project-id": "actual-browserbase-project"
# }

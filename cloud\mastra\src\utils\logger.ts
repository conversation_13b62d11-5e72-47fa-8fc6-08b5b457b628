/**
 * Logging utilities for Guidant Mastra AI Research Engine
 */

import winston from 'winston';

export interface Logger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  warn(message: string, meta?: any): void;
  error(message: string, error?: Error | any): void;
}

/**
 * Create a structured logger instance
 */
export function createLogger(service: string, level: string = 'info'): Logger {
  const logger = winston.createLogger({
    level,
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
        const logEntry = {
          timestamp,
          level,
          service,
          message,
          ...meta
        };
        return JSON.stringify(logEntry);
      })
    ),
    defaultMeta: { service },
    transports: [
      new winston.transports.Console({
        format: process.env['NODE_ENV'] === 'development'
          ? winston.format.combine(
              winston.format.colorize(),
              winston.format.simple()
            )
          : winston.format.json()
      })
    ]
  });

  return {
    debug: (message: string, meta?: any) => logger.debug(message, meta),
    info: (message: string, meta?: any) => logger.info(message, meta),
    warn: (message: string, meta?: any) => logger.warn(message, meta),
    error: (message: string, error?: Error | any) => {
      if (error instanceof Error) {
        logger.error(message, { error: error.message, stack: error.stack });
      } else {
        logger.error(message, { error });
      }
    }
  };
}

/**
 * Create a workflow-specific logger
 */
export function createWorkflowLogger(workflowName: string, runId?: string): Logger {
  const service = `workflow:${workflowName}`;
  const logger = createLogger(service);
  
  return {
    debug: (message: string, meta?: any) => logger.debug(message, { runId, ...meta }),
    info: (message: string, meta?: any) => logger.info(message, { runId, ...meta }),
    warn: (message: string, meta?: any) => logger.warn(message, { runId, ...meta }),
    error: (message: string, error?: Error | any) => logger.error(message, error)
  };
}

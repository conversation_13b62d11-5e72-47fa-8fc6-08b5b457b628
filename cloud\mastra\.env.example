# Mastra Cloud Environment Configuration

# Server Configuration
PORT=8080
NODE_ENV=development
LOG_LEVEL=info

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_REGION=us-central1
VERTEX_PROJECT_ID=your-project-id
VERTEX_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Storage Configuration
STORAGE_TYPE=memory
# For Redis (optional)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# For Firestore (optional)
FIRESTORE_PROJECT_ID=your-project-id

# MCP Server Configuration
TAVILY_API_KEY=your-tavily-api-key
CONTEXT7_API_KEY=your-context7-api-key
STAGEHAND_API_KEY=your-stagehand-api-key

# Guidant Integration
GUIDANT_DOCS_DIR=../../.guidant/docs
GUIDANT_API_URL=http://localhost:4111

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:4111
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Development
ENABLE_PLAYGROUND=true
ENABLE_DEBUG_LOGS=true

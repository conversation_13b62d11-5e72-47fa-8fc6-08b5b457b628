# Guidant Mastra AI - Deployment Checklist

Use this checklist to ensure a successful deployment of the Guidant Mastra AI Research Engine to Google Cloud Run.

## Pre-Deployment Checklist

### ✅ Prerequisites
- [ ] Google Cloud Project created with billing enabled
- [ ] Google Cloud SDK installed and authenticated (`gcloud auth login`)
- [ ] Docker installed and running
- [ ] Required API keys obtained (Tavily, Browserbase, etc.)
- [ ] Upstash Redis instance created and configured

### ✅ Environment Setup
- [ ] All required Google Cloud APIs enabled:
  - [ ] Cloud Build API
  - [ ] Cloud Run API
  - [ ] Container Registry API
  - [ ] Vertex AI API
  - [ ] Firestore API
  - [ ] Secret Manager API
- [ ] Service account created with proper permissions
- [ ] Service account key generated and stored securely

### ✅ Configuration
- [ ] Environment variables configured (see `.env.production.template`)
- [ ] Secrets created in Google Cloud Secret Manager
- [ ] CORS origins configured for production domains
- [ ] Firestore database initialized (if using Firestore)
- [ ] Redis/Upstash connection tested

### ✅ Code Preparation
- [ ] All dependencies installed (`npm install`)
- [ ] TypeScript compilation successful (`npm run build`)
- [ ] Tests passing (`npm test`)
- [ ] Linting clean (`npm run lint`)
- [ ] Docker image builds successfully (`docker build -t test .`)

## Deployment Process

### ✅ Step 1: Build and Push Image
- [ ] Docker image built with production tag
- [ ] Image pushed to Google Container Registry
- [ ] Image scan completed (no critical vulnerabilities)

### ✅ Step 2: Deploy to Cloud Run
- [ ] Cloud Run service deployed successfully
- [ ] Environment variables and secrets configured
- [ ] Service account attached
- [ ] Resource limits set appropriately:
  - [ ] Memory: 4GB
  - [ ] CPU: 2 vCPU
  - [ ] Timeout: 300s
  - [ ] Concurrency: 100
  - [ ] Min instances: 0
  - [ ] Max instances: 10

### ✅ Step 3: Security Configuration
- [ ] HTTPS-only traffic enforced
- [ ] IAM permissions configured (least privilege)
- [ ] Secrets properly referenced (not hardcoded)
- [ ] CORS configured for production domains only
- [ ] Security headers enabled (Helmet.js)

## Post-Deployment Verification

### ✅ Health Checks
- [ ] Basic health endpoint responding (`/health`)
- [ ] Guidant integration health check passing (`/guidant/health`)
- [ ] All workflows listed correctly (`/workflows`)
- [ ] MCP servers connecting successfully

### ✅ Functional Testing
- [ ] Basic research endpoint working (`POST /research`)
- [ ] Guidant research endpoint working (`POST /guidant/research`)
- [ ] Technology workflow execution successful
- [ ] Market research endpoint functional
- [ ] Technical validation endpoint working
- [ ] UX research endpoint operational
- [ ] Error handling working (404, 400 responses)

### ✅ Performance Testing
- [ ] Response times acceptable (< 30s for research queries)
- [ ] Service handles concurrent requests
- [ ] Auto-scaling working correctly
- [ ] Memory usage within limits
- [ ] No memory leaks detected

### ✅ Integration Testing
- [ ] Context7 MCP integration working
- [ ] Tavily MCP integration functional
- [ ] Stagehand MCP integration operational
- [ ] Vertex AI models responding
- [ ] Redis/Upstash connectivity confirmed
- [ ] Firestore operations successful

## Monitoring and Observability

### ✅ Logging
- [ ] Structured JSON logs being generated
- [ ] Log levels appropriate for production
- [ ] Request correlation IDs present
- [ ] Error logs include sufficient context
- [ ] No sensitive data in logs

### ✅ Metrics and Monitoring
- [ ] Cloud Run metrics visible in console
- [ ] Custom application metrics configured
- [ ] Alerting rules set up for:
  - [ ] High error rates
  - [ ] High response times
  - [ ] Service unavailability
  - [ ] Resource exhaustion
- [ ] Uptime monitoring configured

### ✅ Backup and Recovery
- [ ] Data backup strategy implemented (if applicable)
- [ ] Disaster recovery plan documented
- [ ] Service rollback procedure tested

## Security Verification

### ✅ Access Control
- [ ] Service account has minimal required permissions
- [ ] No overly permissive IAM roles
- [ ] API keys stored in Secret Manager only
- [ ] No hardcoded secrets in code or environment

### ✅ Network Security
- [ ] HTTPS enforced for all traffic
- [ ] CORS properly configured
- [ ] Rate limiting enabled
- [ ] Request size limits enforced

### ✅ Data Protection
- [ ] Sensitive data encrypted at rest
- [ ] Secure communication with external APIs
- [ ] PII handling compliant with regulations
- [ ] Data retention policies implemented

## Documentation and Handoff

### ✅ Documentation
- [ ] Deployment guide updated
- [ ] API documentation current
- [ ] Troubleshooting guide available
- [ ] Architecture documentation complete
- [ ] Runbook for operations team

### ✅ Team Handoff
- [ ] Operations team trained on service
- [ ] Monitoring dashboards shared
- [ ] Escalation procedures documented
- [ ] Contact information updated

## Final Verification

### ✅ End-to-End Testing
- [ ] Complete research workflow tested
- [ ] Integration with existing Guidant systems verified
- [ ] User acceptance testing completed
- [ ] Performance benchmarks met

### ✅ Production Readiness
- [ ] Service stable under load
- [ ] All critical paths tested
- [ ] Rollback plan prepared
- [ ] Go-live approval obtained

## Post-Go-Live Tasks

### ✅ Immediate (First 24 hours)
- [ ] Monitor service health continuously
- [ ] Check error rates and response times
- [ ] Verify all integrations working
- [ ] Address any immediate issues

### ✅ Short-term (First week)
- [ ] Analyze usage patterns
- [ ] Optimize resource allocation if needed
- [ ] Gather user feedback
- [ ] Fine-tune monitoring alerts

### ✅ Long-term (First month)
- [ ] Review performance metrics
- [ ] Plan capacity scaling
- [ ] Implement improvements based on usage
- [ ] Update documentation based on learnings

## Emergency Procedures

### ✅ Rollback Plan
- [ ] Previous version image tagged and available
- [ ] Rollback procedure documented and tested
- [ ] Database migration rollback plan (if applicable)
- [ ] Communication plan for service disruption

### ✅ Incident Response
- [ ] Incident response team identified
- [ ] Escalation procedures documented
- [ ] Communication channels established
- [ ] Post-incident review process defined

---

## Deployment Commands Quick Reference

```bash
# Enable APIs
gcloud services enable cloudbuild.googleapis.com run.googleapis.com containerregistry.googleapis.com

# Deploy service
./deploy.sh

# Test deployment
./test-deployment.sh

# View logs
gcloud logs tail "resource.type=cloud_run_revision AND resource.labels.service_name=guidant-mastra-research"

# Update service
gcloud run services update guidant-mastra-research --image gcr.io/PROJECT_ID/guidant-mastra-research:latest --region us-central1
```

## Support Contacts

- **Development Team**: [<EMAIL>]
- **DevOps Team**: [<EMAIL>]
- **On-call Engineer**: [<EMAIL>]

---

**Note**: This checklist should be customized based on your organization's specific requirements and procedures.

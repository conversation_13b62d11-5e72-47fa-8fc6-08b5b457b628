/**
 * Guidant Integration Module for Mastra AI
 * 
 * Provides unified integration layer between Mastra AI workflows
 * and Guidant's existing systems (Research<PERSON><PERSON><PERSON>, DiscoverySessionManager).
 */

import { Mastra } from '@mastra/core';
import { MastraResearchRouterAdapter } from './research-router-adapter';
import { MastraDiscoverySessionAdapter } from './discovery-session-adapter';

export { MastraResearchRouterAdapter } from './research-router-adapter';
export { MastraDiscoverySessionAdapter } from './discovery-session-adapter';

/**
 * Main integration class that coordinates all Guidant integrations
 */
export class GuidantMastraIntegration {
  private mastra: Mastra;
  private researchRouter: MastraResearchRouterAdapter;
  private discoverySession: MastraDiscoverySessionAdapter;

  constructor(
    mastra: Mastra, 
    options: {
      fallbackResearchRouter?: any;
      fallbackDiscoverySessionManager?: any;
    } = {}
  ) {
    this.mastra = mastra;
    this.researchRouter = new MastraResearchRouterAdapter(
      mastra, 
      options.fallbackResearchRouter
    );
    this.discoverySession = new MastraDiscoverySessionAdapter(
      mastra, 
      options.fallbackDiscoverySessionManager
    );
  }

  /**
   * Get the research router adapter
   */
  getResearchRouter(): MastraResearchRouterAdapter {
    return this.researchRouter;
  }

  /**
   * Get the discovery session adapter
   */
  getDiscoverySessionManager(): MastraDiscoverySessionAdapter {
    return this.discoverySession;
  }

  /**
   * Initialize integration with existing Guidant systems
   */
  static async initialize(
    mastra: Mastra,
    options: {
      fallbackResearchRouter?: any;
      fallbackDiscoverySessionManager?: any;
    } = {}
  ): Promise<GuidantMastraIntegration> {
    console.log('Initializing Guidant Mastra AI integration...');

    const integration = new GuidantMastraIntegration(mastra, options);

    // Verify workflows are available
    const requiredWorkflows = [
      'researchOrchestrator',
      'technicalDocumentationWorkflow', 
      'marketResearchWorkflow',
      'uxResearchWorkflow'
    ];

    for (const workflowName of requiredWorkflows) {
      if (!mastra.getWorkflow(workflowName)) {
        console.warn(`Warning: Required workflow '${workflowName}' not found`);
      }
    }

    console.log('Guidant Mastra AI integration initialized successfully');
    return integration;
  }

  /**
   * Health check for the integration
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    workflows: Record<string, boolean>;
    mcpServers: Record<string, boolean>;
    timestamp: string;
  }> {
    const workflows = {
      researchOrchestrator: !!this.mastra.getWorkflow('researchOrchestrator'),
      technicalDocumentationWorkflow: !!this.mastra.getWorkflow('technicalDocumentationWorkflow'),
      marketResearchWorkflow: !!this.mastra.getWorkflow('marketResearchWorkflow'),
      uxResearchWorkflow: !!this.mastra.getWorkflow('uxResearchWorkflow')
    };

    const mcpServers = this.mastra.getMCPServers();
    const mcpServerStatus = {
      context7: !!mcpServers?.context7,
      tavily: !!mcpServers?.tavily,
      stagehand: !!mcpServers?.stagehand
    };

    const workflowsHealthy = Object.values(workflows).every(Boolean);
    const mcpServersHealthy = Object.values(mcpServerStatus).every(Boolean);

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (workflowsHealthy && mcpServersHealthy) {
      status = 'healthy';
    } else if (workflowsHealthy || mcpServersHealthy) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      workflows,
      mcpServers: mcpServerStatus,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Execute a research query using the integrated system
   */
  async executeResearch(
    query: string, 
    context: any = {}
  ): Promise<any> {
    return await this.researchRouter.routeQuery(query, context);
  }

  /**
   * Execute batch research queries
   */
  async executeBatchResearch(
    queries: Array<{query: string, context?: any}>
  ): Promise<any[]> {
    return await this.researchRouter.routeBatch(queries);
  }

  /**
   * Conduct market research for a discovery session
   */
  async conductMarketResearch(
    sessionId: string,
    researchQueries: string[],
    targetMarket: string,
    options: {
      competitors?: string[];
      focusAreas?: string[];
    } = {}
  ): Promise<any> {
    return await this.discoverySession.conductMarketResearch({
      sessionId,
      researchQueries,
      targetMarket,
      competitors: options.competitors || [],
      focusAreas: options.focusAreas || []
    });
  }

  /**
   * Validate technical feasibility for a discovery session
   */
  async validateTechnicalFeasibility(
    sessionId: string,
    technologies: string[],
    projectType: string,
    options: {
      constraints?: string[];
      features?: string[];
    } = {}
  ): Promise<any> {
    return await this.discoverySession.validateTechnicalFeasibility({
      sessionId,
      technologies,
      projectType,
      constraints: options.constraints,
      features: options.features
    });
  }

  /**
   * Conduct UX research for a discovery session
   */
  async conductUXResearch(
    sessionId: string,
    targetUrls: string[],
    analysisType: string = 'comprehensive'
  ): Promise<any> {
    return await this.discoverySession.conductUXResearch(
      sessionId,
      targetUrls,
      analysisType
    );
  }

  /**
   * Get discovery session data
   */
  async getSessionData(sessionId: string): Promise<any> {
    return await this.discoverySession.getSessionData(sessionId);
  }

  /**
   * Update discovery session data
   */
  async updateSessionData(sessionId: string, data: any): Promise<void> {
    return await this.discoverySession.updateSessionData(sessionId, data);
  }
}

/**
 * Factory function to create integration instance
 */
export async function createGuidantIntegration(
  mastra: Mastra,
  options: {
    fallbackResearchRouter?: any;
    fallbackDiscoverySessionManager?: any;
  } = {}
): Promise<GuidantMastraIntegration> {
  return await GuidantMastraIntegration.initialize(mastra, options);
}

/**
 * Type definitions for external use
 */
export interface ResearchQuery {
  query: string;
  context?: {
    sessionId?: string;
    stage?: string;
    projectType?: string;
    targetMarket?: string;
    competitors?: string[];
    technologies?: string[];
    features?: string[];
    priority?: 'low' | 'medium' | 'high';
  };
}

export interface MarketResearchOptions {
  competitors?: string[];
  focusAreas?: string[];
}

export interface TechnicalValidationOptions {
  constraints?: string[];
  features?: string[];
}

export interface IntegrationHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  workflows: Record<string, boolean>;
  mcpServers: Record<string, boolean>;
  timestamp: string;
}

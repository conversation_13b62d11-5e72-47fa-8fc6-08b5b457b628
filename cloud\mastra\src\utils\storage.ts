/**
 * Storage utilities for Guidant Mastra AI Research Engine
 */

import { createClient, type RedisClientType } from 'redis';
import { createLogger } from './logger';

const logger = createLogger('storage');

export interface StorageConfig {
  url: string;
  password?: string;
  db?: number;
}

export interface Storage {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  keys(pattern: string): Promise<string[]>;
  hget(key: string, field: string): Promise<string | null>;
  hset(key: string, field: string, value: string): Promise<void>;
  hdel(key: string, field: string): Promise<void>;
  hgetall(key: string): Promise<Record<string, string>>;
  close(): Promise<void>;
}

/**
 * Create Redis storage client
 */
export async function createRedisStorage(config: StorageConfig): Promise<Storage> {
  logger.info('Connecting to Redis storage...');
  
  const client: RedisClientType = createClient({
    url: config.url,
    password: config.password || undefined,
    database: config.db || 0,
    socket: {
      reconnectStrategy: (retries) => {
        if (retries > 10) {
          logger.error('Redis connection failed after 10 retries');
          return new Error('Redis connection failed');
        }
        return Math.min(retries * 100, 3000);
      }
    }
  });

  client.on('error', (error) => {
    logger.error('Redis client error:', error);
  });

  client.on('connect', () => {
    logger.info('Redis client connected');
  });

  client.on('disconnect', () => {
    logger.warn('Redis client disconnected');
  });

  await client.connect();
  
  return {
    async get(key: string): Promise<string | null> {
      try {
        return await client.get(key);
      } catch (error) {
        logger.error(`Failed to get key ${key}:`, error);
        throw error;
      }
    },

    async set(key: string, value: string, ttl?: number): Promise<void> {
      try {
        if (ttl) {
          await client.setEx(key, ttl, value);
        } else {
          await client.set(key, value);
        }
      } catch (error) {
        logger.error(`Failed to set key ${key}:`, error);
        throw error;
      }
    },

    async del(key: string): Promise<void> {
      try {
        await client.del(key);
      } catch (error) {
        logger.error(`Failed to delete key ${key}:`, error);
        throw error;
      }
    },

    async exists(key: string): Promise<boolean> {
      try {
        const result = await client.exists(key);
        return result === 1;
      } catch (error) {
        logger.error(`Failed to check existence of key ${key}:`, error);
        throw error;
      }
    },

    async keys(pattern: string): Promise<string[]> {
      try {
        return await client.keys(pattern);
      } catch (error) {
        logger.error(`Failed to get keys with pattern ${pattern}:`, error);
        throw error;
      }
    },

    async hget(key: string, field: string): Promise<string | null> {
      try {
        const result = await client.hGet(key, field);
        return result || null;
      } catch (error) {
        logger.error(`Failed to get hash field ${field} from key ${key}:`, error);
        throw error;
      }
    },

    async hset(key: string, field: string, value: string): Promise<void> {
      try {
        await client.hSet(key, field, value);
      } catch (error) {
        logger.error(`Failed to set hash field ${field} in key ${key}:`, error);
        throw error;
      }
    },

    async hdel(key: string, field: string): Promise<void> {
      try {
        await client.hDel(key, field);
      } catch (error) {
        logger.error(`Failed to delete hash field ${field} from key ${key}:`, error);
        throw error;
      }
    },

    async hgetall(key: string): Promise<Record<string, string>> {
      try {
        return await client.hGetAll(key);
      } catch (error) {
        logger.error(`Failed to get all hash fields from key ${key}:`, error);
        throw error;
      }
    },

    async close(): Promise<void> {
      try {
        await client.quit();
        logger.info('Redis connection closed');
      } catch (error) {
        logger.error('Failed to close Redis connection:', error);
        throw error;
      }
    }
  };
}

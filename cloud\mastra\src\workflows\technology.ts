/**
 * Technical Documentation Workflow
 * 
 * Specialized workflow for technical research using Context7 MCP server
 * to access library documentation and technical resources.
 */

import { createWorkflow, createStep } from '@mastra/core/workflows';
import { z } from 'zod';

// Input/Output schemas
const TechnicalInputSchema = z.object({
  query: z.string(),
  taskDescription: z.string().optional(),
  context: z.object({
    sessionId: z.string(),
    technologies: z.array(z.string()).default([]),
    priority: z.enum(['low', 'medium', 'high']).default('medium')
  })
});

const TechnicalOutputSchema = z.object({
  findings: z.string(),
  recommendations: z.array(z.string()),
  sources: z.array(z.string()),
  confidence: z.number(),
  metadata: z.object({
    technologiesAnalyzed: z.array(z.string()),
    processingTime: z.number()
  })
});

// Context7 Research Step
const context7ResearchStep = createStep({
  id: 'context7-research',
  inputSchema: TechnicalInputSchema,
  outputSchema: z.object({
    documentation: z.string(),
    libraries: z.array(z.string()),
    sources: z.array(z.string())
  }),
  execute: async ({ inputData, mastra }) => {
    console.log('Starting Context7 technical research:', inputData.query);

    try {
      // Get MCP servers
      const mcpServers = mastra?.getMCPServers();
      if (!mcpServers?.['context7']) {
        throw new Error('Context7 MCP server not available');
      }

      // For now, simulate Context7 library resolution
      // TODO: Implement proper MCP tool calling pattern
      const libraryResolution = {
        libraries: [{
          id: `/example/${inputData.query}`,
          name: inputData.query,
          description: `Documentation for ${inputData.query}`
        }]
      };

      let documentation = '';
      let libraries: string[] = [];
      let sources: string[] = [];

      if (libraryResolution && libraryResolution.libraries?.[0]) {
        const bestMatch = libraryResolution.libraries[0];

        // Simulate getting detailed documentation
        documentation = `Technical documentation for ${bestMatch.name}: This library provides comprehensive functionality for ${inputData.query}.`;
        libraries = [bestMatch.id];
        sources = [`https://docs.example.com/${inputData.query}`];
      }

      return {
        documentation,
        libraries,
        sources
      };

    } catch (error) {
      console.error('Context7 research failed:', error);
      
      // Fallback research without Context7
      return {
        documentation: `Technical analysis needed for: ${inputData.query}`,
        libraries: [],
        sources: []
      };
    }
  }
});

// Technical Analysis Step
const technicalAnalysisStep = createStep({
  id: 'technical-analysis',
  inputSchema: z.object({
    query: z.string(),
    documentation: z.string(),
    libraries: z.array(z.string()),
    sources: z.array(z.string()),
    context: z.object({
      sessionId: z.string(),
      technologies: z.array(z.string()).default([]),
      priority: z.enum(['low', 'medium', 'high']).default('medium')
    })
  }),
  outputSchema: TechnicalOutputSchema,
  execute: async ({ inputData }) => {
    console.log('Performing technical analysis');

    // Analyze the documentation and provide insights
    const findings = inputData.documentation || `Technical research completed for: ${inputData.query}`;
    
    const recommendations = [
      'Review technical documentation thoroughly',
      'Consider implementation complexity',
      'Evaluate performance implications',
      'Assess security considerations'
    ];

    return {
      findings,
      recommendations,
      sources: inputData.sources,
      confidence: inputData.documentation ? 0.8 : 0.4,
      metadata: {
        technologiesAnalyzed: inputData.libraries,
        processingTime: Date.now()
      }
    };
  }
});

// Create the technical documentation workflow
export const technicalDocumentationWorkflow = createWorkflow({
  id: "technical-documentation-workflow",
  description: "Research technical documentation and libraries using Context7",
  inputSchema: TechnicalInputSchema,
  outputSchema: TechnicalOutputSchema
})
.then(context7ResearchStep)
.then(technicalAnalysisStep);

technicalDocumentationWorkflow.commit();

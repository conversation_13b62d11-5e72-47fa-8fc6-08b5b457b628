#!/bin/bash

# Guidant Mastra AI Research Engine - Cloud Run Deployment Script
# This script builds and deploys the Mastra AI service to Google Cloud Run

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"your-project-id"}
REGION=${GOOGLE_CLOUD_REGION:-"us-central1"}
SERVICE_NAME="guidant-mastra-research"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting deployment of Guidant Mastra AI Research Engine${NC}"

# Check if required environment variables are set
if [ -z "$PROJECT_ID" ] || [ "$PROJECT_ID" = "your-project-id" ]; then
    echo -e "${RED}❌ Error: GOOGLE_CLOUD_PROJECT environment variable is not set${NC}"
    echo "Please set it with: export GOOGLE_CLOUD_PROJECT=your-actual-project-id"
    exit 1
fi

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Error: gcloud CLI is not installed${NC}"
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Error: Docker is not running${NC}"
    echo "Please start Docker and try again"
    exit 1
fi

echo -e "${YELLOW}📋 Configuration:${NC}"
echo "  Project ID: $PROJECT_ID"
echo "  Region: $REGION"
echo "  Service Name: $SERVICE_NAME"
echo "  Image: $IMAGE_NAME"
echo ""

# Set the project
echo -e "${YELLOW}🔧 Setting up Google Cloud project...${NC}"
gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${YELLOW}🔌 Enabling required Google Cloud APIs...${NC}"
gcloud services enable \
    cloudbuild.googleapis.com \
    run.googleapis.com \
    containerregistry.googleapis.com \
    aiplatform.googleapis.com \
    firestore.googleapis.com

# Build the Docker image
echo -e "${YELLOW}🏗️  Building Docker image...${NC}"
docker build -t $IMAGE_NAME:latest .

# Push the image to Google Container Registry
echo -e "${YELLOW}📤 Pushing image to Google Container Registry...${NC}"
docker push $IMAGE_NAME:latest

# Check if secrets exist, create them if they don't
echo -e "${YELLOW}🔐 Checking secrets...${NC}"
if ! gcloud secrets describe mastra-secrets --region=$REGION &> /dev/null; then
    echo -e "${YELLOW}Creating secrets (you'll need to add the actual values later)...${NC}"
    
    # Create empty secrets - user needs to populate these
    echo "placeholder" | gcloud secrets create mastra-secrets --data-file=- --replication-policy="automatic" || true
    
    echo -e "${YELLOW}⚠️  Please update the following secrets with actual values:${NC}"
    echo "  gcloud secrets versions add mastra-secrets --data-file=secrets.json"
    echo ""
    echo "Required secrets in JSON format:"
    echo "{"
    echo '  "upstash-redis-url": "your-upstash-redis-url",'
    echo '  "upstash-redis-token": "your-upstash-redis-token",'
    echo '  "vertex-project-id": "your-vertex-project-id",'
    echo '  "tavily-api-key": "your-tavily-api-key",'
    echo '  "browserbase-api-key": "your-browserbase-api-key",'
    echo '  "browserbase-project-id": "your-browserbase-project-id",'
    echo '  "firestore-project-id": "your-firestore-project-id"'
    echo "}"
fi

# Deploy to Cloud Run
echo -e "${YELLOW}🚀 Deploying to Cloud Run...${NC}"
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_NAME:latest \
    --region $REGION \
    --platform managed \
    --allow-unauthenticated \
    --set-env-vars NODE_ENV=production \
    --set-env-vars PORT=8080 \
    --set-env-vars VERTEX_LOCATION=$REGION \
    --memory 4Gi \
    --cpu 2 \
    --timeout 300s \
    --concurrency 100 \
    --min-instances 0 \
    --max-instances 10

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(status.url)')

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}🌐 Service URL: $SERVICE_URL${NC}"
echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Update secrets with actual API keys and configuration"
echo "2. Test the health endpoint: curl $SERVICE_URL/health"
echo "3. Test a research request: curl -X POST $SERVICE_URL/research -H 'Content-Type: application/json' -d '{\"query\":\"test query\"}'"
echo ""
echo -e "${GREEN}🎉 Guidant Mastra AI Research Engine is now running on Cloud Run!${NC}"
